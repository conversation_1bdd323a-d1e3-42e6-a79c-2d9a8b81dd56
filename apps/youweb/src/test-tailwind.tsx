import React from 'react';

// 测试 Tailwind CSS 任意值语法
export const TestTailwindArbitraryValues = () => {
  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold mb-4">Tailwind 任意值语法测试</h1>
      
      {/* 测试 margin 任意值 */}
      <div className="mr-[0.5rem] mb-[1.5rem] p-4 bg-blue-100 rounded">
        mr-[0.5rem] 和 mb-[1.5rem] 应该正常工作
      </div>
      
      {/* 测试 padding 任意值 */}
      <div className="p-[10px] bg-green-100 rounded mb-4">
        p-[10px] 应该正常工作
      </div>
      
      {/* 测试 width/height 任意值 */}
      <div className="w-[200px] h-[100px] bg-red-100 rounded mb-4 flex items-center justify-center">
        w-[200px] h-[100px] 应该正常工作
      </div>
      
      {/* 测试 color 任意值 */}
      <div className="text-[#ff6b6b] bg-[#4ecdc4] p-4 rounded">
        自定义颜色应该正常工作
      </div>
    </div>
  );
};

export default TestTailwindArbitraryValues;
